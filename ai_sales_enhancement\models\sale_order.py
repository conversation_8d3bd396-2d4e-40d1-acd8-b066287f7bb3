from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Truck number field for client orders
    truck_number = fields.Char(
        string='Truck Number',
        help="Truck number for this order. Clients can specify their preferred truck for delivery."
    )

    # Enhanced product search at order level
    order_product_search = fields.Char(
        string='Add Product/Lot',
        help="Search by product name or lot number to quickly add products to this order"
    )

    @api.onchange('order_product_search')
    def _onchange_order_product_search(self):
        """Enhanced search functionality at order level to add products to order lines"""
        if not self.order_product_search:
            return

        search_term = self.order_product_search.strip()

        # First, try to find by lot number
        lots = self.env['stock.lot'].search([
            ('name', 'ilike', search_term)
        ], limit=10)

        if lots:
            # If exact match found, auto-add to order lines
            exact_lot = lots.filtered(lambda l: l.name.lower() == search_term.lower())
            if len(exact_lot) == 1:
                self._add_product_from_lot(exact_lot[0])
                self.order_product_search = ''  # Clear search after adding
                return

            # If multiple lots found, show selection
            if len(lots) > 1:
                return self._show_lot_selection_for_order(lots)

        # If no lots found, search by product name
        products = self.env['product.product'].search([
            ('name', 'ilike', search_term),
            ('sale_ok', '=', True)
        ], limit=10)

        if products:
            # If exact match found, auto-add to order lines
            exact_product = products.filtered(lambda p: p.name.lower() == search_term.lower())
            if len(exact_product) == 1:
                self._add_product_to_order_line(exact_product[0])
                self.order_product_search = ''  # Clear search after adding
                return

            # If multiple products found, show selection
            if len(products) > 1:
                return self._show_product_selection_for_order(products)

        # No matches found
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('No Results'),
                'message': _('No products or lots found for "%s"') % search_term,
                'type': 'warning',
            }
        }

    def _add_product_from_lot(self, lot):
        """Add product to order line from selected lot"""
        self.ensure_one()

        # Create new order line with the product from the lot
        line_vals = {
            'order_id': self.id,
            'product_id': lot.product_id.id,
            'product_uom_qty': 1.0,  # Default quantity
            'selected_lot_id': lot.id,
        }

        # Find best location for this lot
        quants = lot.quant_ids.filtered(lambda q: q.quantity > 0)
        if quants:
            best_location = quants.sorted('quantity', reverse=True)[0].location_id
            line_vals['selected_location_id'] = best_location.id

        self.env['sale.order.line'].create(line_vals)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Product Added'),
                'message': _('Added %s (Lot: %s) to order lines') % (lot.product_id.name, lot.name),
                'type': 'success',
            }
        }

    def _add_product_to_order_line(self, product):
        """Add product to order line"""
        self.ensure_one()

        line_vals = {
            'order_id': self.id,
            'product_id': product.id,
            'product_uom_qty': 1.0,  # Default quantity
        }

        self.env['sale.order.line'].create(line_vals)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Product Added'),
                'message': _('Added %s to order lines') % product.name,
                'type': 'success',
            }
        }

    def _show_lot_selection_for_order(self, lots):
        """Show lot selection wizard for order level search"""
        return {
            'name': _('Select Lot'),
            'type': 'ir.actions.act_window',
            'res_model': 'order.product.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_search_term': self.order_product_search,
                'default_lot_ids': [(6, 0, lots.ids)],
                'search_type': 'lot'
            }
        }

    def _show_product_selection_for_order(self, products):
        """Show product selection wizard for order level search"""
        return {
            'name': _('Select Product'),
            'type': 'ir.actions.act_window',
            'res_model': 'order.product.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_search_term': self.order_product_search,
                'default_product_ids': [(6, 0, products.ids)],
                'search_type': 'product'
            }
        }

    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search to include truck number"""
        if args is None:
            args = []

        # If searching and truck number field exists, include it in search
        if name and hasattr(self, 'truck_number'):
            truck_domain = [('truck_number', operator, name)]
            truck_orders = self.search(truck_domain + args, limit=limit)

            if truck_orders:
                # Combine with standard name search
                standard_results = super(SaleOrder, self).name_search(name, args, operator, limit)

                # Create combined results, avoiding duplicates
                truck_results = [(order.id, f"{order.name} (Truck: {order.truck_number})")
                               for order in truck_orders if order.truck_number]

                # Merge results, removing duplicates
                all_ids = set()
                combined_results = []

                for result in truck_results + standard_results:
                    if result[0] not in all_ids:
                        all_ids.add(result[0])
                        combined_results.append(result)

                return combined_results[:limit]

        return super(SaleOrder, self).name_search(name, args, operator, limit)

    def action_open_lot_location_wizard(self):
        """Open wizard for lot number and location selection after confirmation"""
        self.ensure_one()
        
        if self.state not in ['sale', 'done']:
            raise UserError(_('This action is only available for confirmed sales orders.'))
        
        # Get all sale order lines that need lot/location assignment
        lines_needing_assignment = self.order_line.filtered(
            lambda l: l.product_id.tracking in ['lot', 'serial'] and l.product_uom_qty > 0
        )
        
        if not lines_needing_assignment:
            raise UserError(_('No products requiring lot number assignment found in this order.'))
        
        return {
            'name': _('Select Lot Numbers and Locations'),
            'type': 'ir.actions.act_window',
            'res_model': 'lot.location.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_sale_order_line_ids': [(6, 0, lines_needing_assignment.ids)],
            }
        }


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Enhanced search field for product/lot search
    product_lot_search = fields.Char(
        string='Product/Lot Search',
        help="Search by product name or lot number. System will auto-fill product, lot, and location."
    )

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to ensure order_id is always set"""
        for vals in vals_list:
            if not vals.get('order_id'):
                # Try to get order_id from context
                order_id = self._context.get('default_order_id')
                if order_id:
                    vals['order_id'] = order_id
                else:
                    # If no order_id available, this is an error
                    raise UserError(_('Order reference is required when creating sale order lines.'))

        return super(SaleOrderLine, self).create(vals_list)
    
    # Fields to store selected lot and location
    selected_lot_id = fields.Many2one(
        'stock.lot',
        string='Selected Lot',
        help="Lot number selected for this line"
    )
    
    selected_location_id = fields.Many2one(
        'stock.location',
        string='Selected Location',
        help="Location selected for this line"
    )
    
    # Display fields for better UX
    available_qty_at_location = fields.Float(
        string='Available Qty',
        compute='_compute_available_qty_at_location',
        help="Available quantity at selected location"
    )
    
    lot_weight = fields.Float(
        string='Lot Weight',
        compute='_compute_lot_weight',
        help="Total weight of the selected lot"
    )

    @api.depends('selected_lot_id', 'selected_location_id')
    def _compute_available_qty_at_location(self):
        """Compute available quantity at selected location for selected lot"""
        for line in self:
            try:
                if line.selected_lot_id and line.selected_location_id and line.product_id:
                    quants = self.env['stock.quant'].search([
                        ('product_id', '=', line.product_id.id),
                        ('lot_id', '=', line.selected_lot_id.id),
                        ('location_id', '=', line.selected_location_id.id),
                    ])
                    line.available_qty_at_location = sum(quants.mapped('quantity'))
                else:
                    line.available_qty_at_location = 0.0
            except Exception:
                # If there's any error (like missing order_id), set to 0
                line.available_qty_at_location = 0.0

    @api.depends('selected_lot_id')
    def _compute_lot_weight(self):
        """Compute total weight of selected lot"""
        for line in self:
            try:
                if line.selected_lot_id:
                    line.lot_weight = line.selected_lot_id.weight
                else:
                    line.lot_weight = 0.0
            except Exception:
                # If there's any error, set to 0
                line.lot_weight = 0.0

    @api.onchange('product_lot_search')
    def _onchange_product_lot_search(self):
        """Enhanced search functionality for product/lot search"""
        if not self.product_lot_search:
            self.product_id = False
            self.selected_lot_id = False
            self.selected_location_id = False
            return

        # Check if we have a valid order_id context or if this is a new line
        if not self.order_id and not self._context.get('default_order_id'):
            # If no order context, just clear the search and return
            self.product_lot_search = ''
            return {
                'warning': {
                    'title': _('Order Required'),
                    'message': _('Please save the order first before searching for products/lots.'),
                }
            }

        search_term = self.product_lot_search.strip()

        # First, try to find by lot number
        lots = self.env['stock.lot'].search([
            ('name', 'ilike', search_term)
        ], limit=10)

        if lots:
            # If exact match found, auto-select
            exact_lot = lots.filtered(lambda l: l.name.lower() == search_term.lower())
            if len(exact_lot) == 1:
                self._auto_select_lot_and_location(exact_lot[0])
                return

            # If multiple lots found, show selection
            if len(lots) > 1:
                return self._show_lot_selection_notification(lots)

        # If no lots found, search by product name
        products = self.env['product.product'].search([
            '|',
            ('name', 'ilike', search_term),
            ('default_code', 'ilike', search_term)
        ], limit=10)

        if products:
            # If exact match found, auto-select
            exact_product = products.filtered(
                lambda p: p.name.lower() == search_term.lower() or
                         (p.default_code and p.default_code.lower() == search_term.lower())
            )
            if len(exact_product) == 1:
                self.product_id = exact_product[0]
                self._find_best_lot_for_product(exact_product[0])
                return

            # If multiple products found, auto-select first one
            if len(products) == 1:
                self.product_id = products[0]
                self._find_best_lot_for_product(products[0])
                return

    def _auto_select_lot_and_location(self, lot):
        """Auto-select lot and find best location with stock"""
        try:
            self.product_id = lot.product_id
            self.selected_lot_id = lot

            # Find location with highest stock for this lot
            quants = self.env['stock.quant'].search([
                ('product_id', '=', lot.product_id.id),
                ('lot_id', '=', lot.id),
                ('quantity', '>', 0),
            ], order='quantity desc', limit=1)

            if quants:
                self.selected_location_id = quants[0].location_id

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Auto-Selected'),
                    'message': _('Product: %s, Lot: %s, Location: %s') % (
                        lot.product_id.name, lot.name,
                        quants[0].location_id.name if quants else _('No stock location')
                    ),
                    'type': 'success',
                }
            }
        except Exception as e:
            # If there's an error (like missing order_id), just set the basic fields
            self.product_id = lot.product_id
            self.selected_lot_id = lot
            return {
                'warning': {
                    'title': _('Partial Selection'),
                    'message': _('Product and lot selected. Location will be set after saving the line.'),
                }
            }

    def _find_best_lot_for_product(self, product):
        """Find best available lot for selected product"""
        if product.tracking not in ['lot', 'serial']:
            return

        try:
            # Find lots with available stock
            quants = self.env['stock.quant'].search([
                ('product_id', '=', product.id),
                ('quantity', '>', 0),
                ('lot_id', '!=', False),
            ], order='quantity desc', limit=1)

            if quants:
                self.selected_lot_id = quants[0].lot_id
                self.selected_location_id = quants[0].location_id
        except Exception:
            # If there's an error (like missing order_id), just skip lot assignment
            pass

    def _show_lot_selection_notification(self, lots):
        """Show notification when multiple lots are found"""
        lot_names = ', '.join(lots.mapped('name')[:5])
        if len(lots) > 5:
            lot_names += f' and {len(lots) - 5} more...'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Multiple Lots Found'),
                'message': _('Found lots: %s. Please be more specific or use the lot selection wizard.') % lot_names,
                'type': 'info',
            }
        }

    def action_open_lot_selection_wizard(self):
        """Open wizard to select from available lots"""
        self.ensure_one()
        
        if not self.product_id:
            raise UserError(_('Please select a product first.'))
        
        return {
            'name': _('Select Lot and Location'),
            'type': 'ir.actions.act_window',
            'res_model': 'lot.location.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_line_ids': [(6, 0, [self.id])],
                'default_product_id': self.product_id.id,
            }
        }
