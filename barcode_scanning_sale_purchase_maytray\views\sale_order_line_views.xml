<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--    View for sale order-->
    <record id="view_order_form" model="ir.ui.view">
        <field name="name">
            sale.order.view.form.inherit.barcode.scanning.sale.purchase
        </field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']"
                   position="before">
                <field name="barcode_scan" string="Lot No"/>
                <field name="filtered_product_ids" invisible="1"/>
                <button name="show_lot_products" string="Select Product" type="object"
                        icon="fa-search" attrs="{'invisible': [('barcode_scan', '=', False)]}"
                        help="Show all products associated with this lot number"/>
            </xpath>



            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']"
                   position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
        </field>
    </record>
</odoo>
