<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="view_product_template_form_inherit_bag" model="ir.ui.view">
            <field name="name">product.template.form.inherit.bag</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='categ_id']" position="after">
                    <field name="x_is_bag"/>
                    <field name="x_bag_weight" invisible="x_is_bag != True"/>
                    <field name="raw_material"/>
                    <field name="other_material"/>
                </xpath>
            </field>
        </record>

        <record id="purchase_order_line_form_view" model="ir.ui.view">
            <field name="name">purchase.order.line.form</field>
            <field name="model">purchase.order.line</field>
            <field name="inherit_id" ref="purchase.purchase_order_line_form2"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='product_qty']" position="after">
                    <field name="x_bag"/>
                    <field name="x_bag_quantity"/>

                </xpath>
            </field>
        </record>

        <record id="purchase_order_form_view" model="ir.ui.view">
            <field name="name">purchase.order.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                
                <!-- Insert Thekedar field after partner_id -->
                <!-- <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="x_thekedar_id" string="Thekedar" 
                        domain="[('x_is_thekedar', '=', True)]"
                        context="{'search_default_x_is_thekedar': 1}"
                        invisible="1"/>
                </xpath> -->

                <xpath expr="//div[@name='reminder']" position="after">
                    <separator string="Commission and Market Fees"/>
                    
                        <field name="x_commission_fees"/>
                        <field name="x_commission_fees_values" invisible="x_commission_fees != True"/>
                        <field name="x_market_fees"/>
                        <field name="x_market_fees_values" invisible="x_market_fees != True"/>
                        <field name="x_brokerage_fees"/>
                        <field name="x_brokerage_fees_values" invisible="x_brokerage_fees != True"/>    
                        <field name="x_broker_id" string="Broker" 
                            domain="[('x_is_broker', '=', True)]"
                            invisible="x_brokerage_fees != True"
                            context="{'search_default_x_is_broker': 1}"/>
                    
                </xpath>
                <xpath expr="//field[@name='currency_id']" position="after">
                    <separator string="Transportation and Thekedar Details"/>
                    
                        <field name="x_transportation_fees"/>
                        <field name="x_transportation_fees_value" invisible="x_transportation_fees != True"/>
                        <field name="x_thekedar_fees"/>
                        <field name="x_thekedar_fees_value" invisible="x_thekedar_fees != True"/>
                        <field name="x_thekedar_id" string="Thekedar" 
                            domain="[('x_is_thekedar', '=', True)]"
                            invisible="x_thekedar_fees != True"
                            context="{'search_default_x_is_thekedar': 1}"/>
                    
                </xpath>
                <xpath expr="//field[@name='order_line']/list//field[@name='taxes_id']" position="after">
                    <field name="x_bag"/>
                    <field name="x_bag_quantity"/>

                    <!-- <field name="x_product_weight" readonly="1"/> -->
                    <field name="x_product_loose_weight" readonly="state != 'draft'"/>
                    <field name="x_product_total_weight" readonly="1"/>
                    <!-- <field name="x_product_rate"/> -->
                </xpath>
                <xpath expr="//field[@name='order_line']/list//field[@name='product_uom']" position="attributes">
                <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="before">
                    <field name="x_product_rate"/>
                    <!-- <field name="x_quintal_rate" optional="hide"/> -->
                    <field name="x_bank_payment"/>
                    <field name="x_cash_payment"/>
                </xpath>
                <xpath expr="//field[@name='order_line']/list//field[@name='taxes_id']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='order_line']/list//field[@name='state']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>

                <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>

                <xpath expr="//field[@name='tax_totals']" position="before">
                    <field name="purchase_cost" readonly="1"/>
                    <field name="other_cost" readonly="1"/>
                    <field name="total_bank_payment"/>
                    <field name="total_cash_payment"/>
                    
                </xpath>
            </field>
        </record>

        <!-- <record id="view_move_form_inherit_total_fields" model="ir.ui.view">
            <field name="name">account.move.form.inherit.total.fields</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='tax_totals']" position="after">                    
                        <field name="total_bank_payment" readonly="1" invisible="move_type != 'in_invoice'"/>                    
                        <field name="total_cash_payment" readonly="1" invisible="move_type != 'in_invoice'"/>
                        <field name="total_cash_paid" readonly="1" invisible="move_type != 'in_invoice'"/>
                        <field name="total_bank_paid" readonly="1" invisible="move_type != 'in_invoice'"/>
                        <field name="total_pending_cash" readonly="1" invisible="move_type != 'in_invoice'"/>
                        <field name="total_pending_bank" readonly="1" invisible="move_type != 'in_invoice'"/>
                </xpath>
            </field>
        </record> -->

        <record id="view_picking_form_inherit_weight" model="ir.ui.view">
            <field name="name">stock.picking.form.inherit.weight</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="inside">
                    <group string="Weight Information" class="oe_clear">
                        <field name="x_total_purchase_weight" readonly="1"/>
                        <field name="x_total_bags_weight" readonly="1"/>
                        <field name="x_weighbridge_weight" />
                        <field name="x_difference_weight" decoration-danger="x_difference_weight &lt; 0"
                               decoration-success="x_difference_weight &gt; 0" readonly="1"/>
                    </group>
                </xpath>
            </field>
        </record>

        <record id="view_thekedar_cost_form" model="ir.ui.view">
            <field name="name">thekedar.cost.form</field>
            <field name="model">thekedar.cost</field>
            <field name="arch" type="xml">
                <form string="Thekedar Costs">
                    <group>
                        <field name="purchase_order_id"/>
                        <field name="thekedar_id"/>
                        <field name="purchase_date"/>
                        <field name="inventory_received_date"/>
                        <field name="amount_to_be_paid"/>
                        <field name="amount_paid"/>
                        <field name="due_amount" readonly="1"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="view_thekedar_cost_tree" model="ir.ui.view">
            <field name="name">thekedar.cost.tree</field>
            <field name="model">thekedar.cost</field>
            <field name="arch" type="xml">
                <list string="Thekedar Costs">
                    <field name="purchase_order_id"/>
                    <field name="thekedar_id"/>
                    <field name="purchase_date"/>
                    <field name="inventory_received_date"/>
                    <field name="amount_to_be_paid"/>
                    <field name="amount_paid"/>
                    <field name="due_amount" readonly="1"/>
                </list>
            </field>
        </record>

        <record id="action_thekedar_cost" model="ir.actions.act_window">
            <field name="name">Thekedar Costs</field>
            <field name="res_model">thekedar.cost</field>
            <field name="view_mode">list,form</field>
        </record>

        <menuitem id="menu_thekedar_cost_root" name="Thekedar Costs" parent="purchase.menu_purchase_root" action="action_thekedar_cost"/>
        
        <record id="view_broker_cost_form" model="ir.ui.view">
            <field name="name">broker.cost.form</field>
            <field name="model">broker.cost</field>
            <field name="arch" type="xml">
                <form string="Broker Costs">
                    <group>
                        <field name="purchase_order_id"/>
                        <field name="broker_id"/>
                        <field name="purchase_date"/>
                        <field name="inventory_received_date"/>
                        <field name="amount_to_be_paid"/>
                        <field name="amount_paid"/>
                        <field name="due_amount" readonly="1"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="view_broker_cost_tree" model="ir.ui.view">
            <field name="name">broker.cost.tree</field>
            <field name="model">broker.cost</field>
            <field name="arch" type="xml">
                <list string="Broker Costs">
                    <field name="purchase_order_id"/>
                    <field name="broker_id"/>
                    <field name="purchase_date"/>
                    <field name="inventory_received_date"/>
                    <field name="amount_to_be_paid"/>
                    <field name="amount_paid"/>
                    <field name="due_amount" readonly="1"/>
                </list>
            </field>
        </record>

        <record id="action_broker_cost" model="ir.actions.act_window">
            <field name="name">Broker Costs</field>
            <field name="res_model">broker.cost</field>
            <field name="view_mode">list,form</field>
        </record>

        <menuitem id="menu_broker_cost_root" name="Broker Costs" parent="purchase.menu_purchase_root" action="action_broker_cost"/>
        
        <record id="view_partner_form_inherit_broker" model="ir.ui.view">
            <field name="name">res.partner.form.inherit.broker</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='category_id']" position="after">                    
                        <field name="x_is_thekedar"/> 
                        <field name="x_is_broker"/>                   
                </xpath>
            </field>
        </record>
    </data>
</odoo>